import scrapy
from ainav_scrapers.items import AiTool<PERSON>eadItem
from urllib.parse import urljoin, urlparse
import datetime
import re
import logging

class ToolifySpider(scrapy.Spider):
    name = "toolify"
    allowed_domains = ["toolify.ai"]

    # Start with main page and key category pages
    start_urls = [
        "https://toolify.ai/",
        "https://toolify.ai/new",
        "https://toolify.ai/most-saved",
        "https://toolify.ai/most-used",
        "https://toolify.ai/category?group=writing-editing",
        "https://toolify.ai/category?group=image-generation-editing",
        "https://toolify.ai/category?group=coding-development",
        "https://toolify.ai/category?group=video-animation",
        "https://toolify.ai/category?group=business-management",
        "https://toolify.ai/category?group=marketing-advertising",
    ]

    custom_settings = {
        'AUTOTHROTTLE_ENABLED': True,
        'AUTOTHROTTLE_START_DELAY': 3,
        'AUTOTHROTTLE_MAX_DELAY': 15,
        'DOWNLOAD_DELAY': 2,
        'CONCURRENT_REQUESTS_PER_DOMAIN': 2,
        'USER_AGENT': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36',
        'FEEDS': {
            'toolify_leads.jsonl': {
                'format': 'jsonlines',
                'encoding': 'utf8',
                'store_empty': False,
                'fields': ['tool_name_on_directory', 'external_website_url', 'source_directory', 'scraped_date'],
                'indent': None,
            }
        },
        'CLOSESPIDER_ITEMCOUNT': 200,  # Reasonable limit for testing
        'ROBOTSTXT_OBEY': True,
    }

    def __init__(self, max_items=None, start_page=1, max_pages=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.max_items = int(max_items) if max_items else 200
        self.start_page = int(start_page) if start_page else 1
        self.max_pages = int(max_pages) if max_pages else None
        self.current_page = self.start_page
        self.scraped_tools = set()
        self.total_found = 0
        
        # Update item limit in settings
        if self.max_items:
            self.custom_settings['CLOSESPIDER_ITEMCOUNT'] = self.max_items
        
        self.logger.info(f"🕷️ Toolify Spider initialized:")
        self.logger.info(f"   Max items: {self.max_items}")
        self.logger.info(f"   Start page: {self.start_page}")
        self.logger.info(f"   Max pages: {self.max_pages}")

    def start_requests(self):
        """Generate initial requests"""
        for url in self.start_urls:
            yield scrapy.Request(
                url,
                callback=self.parse_list_page,
                meta={
                    "page_number": 1
                }
            )

    async def parse_list_page(self, response):
        """Parse tool listing pages"""
        page = response.meta.get("playwright_page")
        if page:
            # Wait for content to load
            try:
                await page.wait_for_selector('a[href*="/tool/"]', timeout=10000)
            except:
                self.logger.warning(f"Timeout waiting for tool links on {response.url}")
            await page.close()

        page_number = response.meta.get("page_number", 1)
        self.logger.info(f"🔍 Parsing Toolify page {page_number}: {response.url}")

        if response.status != 200:
            self.logger.error(f"Failed to load page {response.url} with status {response.status}")
            return

        # Extract tool links using multiple strategies
        tool_links = self._extract_tool_links(response)
        
        self.logger.info(f"Found {len(tool_links)} tool links on page {page_number}")

        # Process each tool link
        for tool_info in tool_links:
            if self.total_found >= self.max_items:
                self.logger.info(f"Reached max items limit ({self.max_items})")
                return
                
            tool_key = f"{tool_info['name']}:{tool_info['url']}"
            if tool_key not in self.scraped_tools:
                self.scraped_tools.add(tool_key)
                self.total_found += 1
                
                yield scrapy.Request(
                    tool_info['url'],
                    callback=self.parse_tool_detail,
                    meta={
                        "playwright": True,
                        "playwright_include_page": True,
                        "tool_name": tool_info['name'],
                        "category": tool_info.get('category', 'AI Tools'),
                        "page_url": response.url
                    }
                )

        # Handle pagination if we haven't reached limits
        if (self.max_pages is None or page_number < self.max_pages) and self.total_found < self.max_items:
            # Look for pagination or load more functionality
            # Toolify might use infinite scroll or pagination
            next_page_found = False
            
            # Check for pagination links
            pagination_selectors = [
                'a[href*="page="]',
                '.pagination a',
                '.next-page',
                'button[data-page]'
            ]
            
            for selector in pagination_selectors:
                next_links = response.css(selector)
                if next_links:
                    for link in next_links:
                        href = link.css('::attr(href)').get()
                        if href and 'page=' in href:
                            next_url = response.urljoin(href)
                            yield scrapy.Request(
                                next_url,
                                callback=self.parse_list_page,
                                meta={
                                    "playwright": True,
                                    "playwright_include_page": True,
                                    "page_number": page_number + 1
                                }
                            )
                            next_page_found = True
                            break
                if next_page_found:
                    break

    def _extract_tool_links(self, response):
        """Extract tool links from the page using multiple strategies"""
        tools = []
        
        # Strategy 1: Direct tool links
        tool_link_selectors = [
            'a[href*="/tool/"]',
            'a[href^="/tool/"]'
        ]
        
        for selector in tool_link_selectors:
            links = response.css(selector)
            for link in links:
                href = link.css('::attr(href)').get()
                if href and '/tool/' in href:
                    # Extract tool name from various possible locations
                    tool_name = self._extract_tool_name_from_link(link, response)
                    if tool_name and len(tool_name) > 2:
                        full_url = response.urljoin(href)
                        tools.append({
                            'name': tool_name,
                            'url': full_url,
                            'category': self._extract_category_from_page(response)
                        })
        
        # Strategy 2: Look for tool cards/containers
        card_selectors = [
            'div',  # We'll filter these manually
            'article',  # We'll filter these manually
            '.tool-card',
            '.ai-tool-card'
        ]
        
        for selector in card_selectors:
            cards = response.css(selector)
            for card in cards:
                # Check if this card contains a tool link
                tool_link = card.css('a[href*="/tool/"]::attr(href)').get()
                if tool_link:
                    tool_name = self._extract_tool_name_from_card(card)
                    if tool_name and len(tool_name) > 2:
                        full_url = response.urljoin(tool_link)
                        # Avoid duplicates
                        if not any(t['url'] == full_url for t in tools):
                            tools.append({
                                'name': tool_name,
                                'url': full_url,
                                'category': self._extract_category_from_page(response)
                            })
        
        return tools

    def _extract_tool_name_from_link(self, link, response):
        """Extract tool name from link element"""
        # Try different text extraction methods
        name_candidates = [
            link.css('::text').get(),
            link.css('img::attr(alt)').get(),
            link.css('::attr(title)').get(),
        ]
        
        for candidate in name_candidates:
            if candidate:
                name = candidate.strip()
                if len(name) > 2 and not name.lower() in ['more', 'read more', 'learn more', 'visit']:
                    return name
        
        # Fallback: extract from URL
        href = link.css('::attr(href)').get()
        if href and '/tool/' in href:
            tool_slug = href.split('/tool/')[-1].split('?')[0].split('#')[0]
            return tool_slug.replace('-', ' ').title()
        
        return None

    def _extract_tool_name_from_card(self, card):
        """Extract tool name from card element"""
        name_selectors = [
            'h1::text', 'h2::text', 'h3::text', 'h4::text',
            '.tool-name::text', '.title::text', '.name::text',
            'a[href*="/tool/"]::text',
            'img::attr(alt)'
        ]
        
        for selector in name_selectors:
            name = card.css(selector).get()
            if name:
                name = name.strip()
                if len(name) > 2 and not name.lower() in ['more', 'read more', 'learn more']:
                    return name
        
        return None

    def _extract_category_from_page(self, response):
        """Extract category from page URL or content"""
        url = response.url
        
        # Extract from URL parameters
        if 'group=' in url:
            group = url.split('group=')[-1].split('&')[0]
            return group.replace('-', ' ').title()
        
        # Extract from page title or headers
        title = response.css('title::text').get()
        if title:
            if 'Writing' in title:
                return 'Writing & Editing'
            elif 'Image' in title:
                return 'Image Generation'
            elif 'Code' in title or 'Development' in title:
                return 'Coding & Development'
        
        return 'AI Tools'

    async def parse_tool_detail(self, response):
        """Parse individual tool detail pages"""
        page = response.meta.get("playwright_page")
        if page:
            await page.close()

        tool_name = response.meta.get("tool_name", "Unknown")
        category = response.meta.get("category", "AI Tools")
        page_url = response.meta.get("page_url", "")
        
        self.logger.info(f"📄 Parsing tool detail: {tool_name} at {response.url}")

        if response.status != 200:
            self.logger.error(f"Failed to load tool detail page {response.url} with status {response.status}")
            return

        # Extract external website URL
        external_url = self._extract_external_url(response)
        
        if not external_url:
            self.logger.warning(f"Could not find external URL for {tool_name} at {response.url}")
            # Use the tool page URL as fallback
            external_url = response.url

        # Create item
        item = AiToolLeadItem()
        item['tool_name_on_directory'] = tool_name
        item['external_website_url'] = external_url
        item['source_directory'] = "toolify.ai"
        item['scraped_date'] = datetime.datetime.now(datetime.timezone.utc).isoformat()

        self.logger.info(f"✅ Extracted: {tool_name} -> {external_url}")
        yield item

    def _extract_external_url(self, response):
        """Extract the external website URL from tool detail page"""
        # Multiple strategies to find the external URL
        url_selectors = [
            # Common patterns for external links
            'a[href^="http"]:not([href*="toolify.ai"])',
            'a[data-url]::attr(data-url)',
            'a[data-href]::attr(data-href)',
            'a.external-link::attr(href)',
            'a.website-link::attr(href)',
            'a.official-link::attr(href)',
            'a[href*="utm_source=toolify"]::attr(href)',
            
            # Look for buttons or links with "visit" text
            'a:contains("Visit")::attr(href)',
            'a:contains("Website")::attr(href)',
            'a:contains("Official")::attr(href)',
            'button[data-url]::attr(data-url)',
        ]
        
        for selector in url_selectors:
            urls = response.css(selector).getall()
            for url in urls:
                if url and url.startswith('http') and 'toolify.ai' not in url:
                    # Clean up tracking parameters but keep the base URL
                    clean_url = url.split('?')[0] if '?' in url else url
                    return clean_url
        
        # Fallback: look for any external links in the page
        all_links = response.css('a[href^="http"]::attr(href)').getall()
        for link in all_links:
            if link and 'toolify.ai' not in link and not any(domain in link for domain in ['twitter.com', 'facebook.com', 'linkedin.com', 'youtube.com']):
                return link.split('?')[0] if '?' in link else link
        
        return None
